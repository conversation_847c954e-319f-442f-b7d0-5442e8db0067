export default ({ env }) => [
    "strapi::errors",
    {
        name: "strapi::security",
        config: {
            contentSecurityPolicy: {
                useDefaults: true,
                directives: {
                    "default-src": ["'self'"],
                    "script-src": ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
                    "style-src": ["'self'", "'unsafe-inline'"],
                    "connect-src": ["'self'", "https:"],
                    "img-src": ["'self'", "data:", "blob:", "https:"],
                    "media-src": ["'self'", "data:", "blob:", "https:"],
                    "font-src": ["'self'", "https:", "data:"],
                    "object-src": ["'none'"],
                    "base-uri": ["'self'"],
                    "form-action": ["'self'"],
                    "frame-ancestors": ["'none'"],
                    upgradeInsecureRequests: null,
                },
            },
            hsts: {
                enabled: true,
                maxAge: 31536000,
                includeSubDomains: true,
                preload: true,
            },
            frameguard: {
                enabled: true,
                value: "SAMEORIGIN",
            },
            xssFilter: {
                enabled: true,
            },
            noSniff: {
                enabled: true,
            },
            referrerPolicy: {
                enabled: true,
                value: "strict-origin-when-cross-origin",
            },
        },
    },
    {
        name: "strapi::cors",
        config: {
            headers: "*",
            origin: env("FRONTEND_URL", "http://localhost:3000").split(","),
            credentials: true,
        },
    },
    "strapi::poweredBy",
    "strapi::logger",
    "strapi::query",
    {
        name: "strapi::body",
        config: {
            formLimit: "256mb",
            jsonLimit: "256mb",
            textLimit: "256mb",
            formidable: {
                maxFileSize: 250 * 1024 * 1024, // 250mb
            },
        },
    },
    "strapi::session",
    "strapi::favicon",
    "strapi::public",
];
