module.exports = {
    apps: [
        {
            name: "civicpoll-backend",
            cwd: "./apps/backend",
            script: "pnpm",
            args: "start",
            instances: 1,
            exec_mode: "fork", // Strapi doesn't support cluster mode well
            env: {
                NODE_ENV: "development",
                PORT: 1347,
            },
            env_development: {
                NODE_ENV: "development",
                PORT: 1347,
                watch: true,
                ignore_watch: ["node_modules", "public/uploads", ".tmp", "logs", "build"],
            },
            env_staging: {
                NODE_ENV: "staging",
                PORT: 1357,
                instances: 1,
            },
            env_production: {
                NODE_ENV: "production",
                PORT: 1337,
                instances: 1,
            },
            error_file: "/opt/civicpoll/logs/backend-error.log",
            out_file: "/opt/civicpoll/logs/backend-out.log",
            log_date_format: "YYYY-MM-DD HH:mm:ss Z",
            max_memory_restart: "1G",
            // Graceful shutdown
            kill_timeout: 5000,
            listen_timeout: 5000,
        },
        {
            name: "civicpoll-frontend",
            cwd: "./apps/frontend",
            script: "pnpm",
            args: "start",
            instances: 2,
            exec_mode: "cluster",
            env: {
                NODE_ENV: "development",
                PORT: 3010,
            },
            env_development: {
                NODE_ENV: "development",
                PORT: 3010,
                watch: false, // Next.js has its own file watching
            },
            env_staging: {
                NODE_ENV: "staging",
                PORT: 3020,
                instances: 1,
            },
            env_production: {
                NODE_ENV: "production",
                PORT: 3000,
                instances: 2,
            },
            error_file: "/opt/civicpoll/logs/frontend-error.log",
            out_file: "/opt/civicpoll/logs/frontend-out.log",
            log_date_format: "YYYY-MM-DD HH:mm:ss Z",
            max_memory_restart: "800M",
            // Graceful shutdown
            kill_timeout: 5000,
            listen_timeout: 5000,
        },
    ],

    // Deploy configuration
    deploy: {
        development: {
            user: "deploy",
            host: "civicpoll-dev.fr.smatflow.xyz",
            ref: "origin/main",
            repo: "**************:smatflow/civicpoll.git",
            path: "/opt/civicpoll-dev",
            "pre-deploy-local": "",
            "post-deploy":
                "pnpm install --frozen-lockfile && pnpm build && pm2 reload ecosystem.config.js --env development",
            "pre-setup": "",
        },
        staging: {
            user: "deploy",
            host: "civicpoll-staging.fr.smatflow.xyz",
            ref: "origin/main",
            repo: "**************:smatflow/civicpoll.git",
            path: "/opt/civicpoll-staging",
            "pre-deploy-local": "",
            "post-deploy":
                "pnpm install --frozen-lockfile && pnpm build && pm2 reload ecosystem.config.js --env staging",
            "pre-setup": "",
        },
        production: {
            user: "deploy",
            host: "civicpoll.fr.smatflow.xyz",
            ref: "origin/main",
            repo: "**************:smatflow/civicpoll.git",
            path: "/opt/civicpoll",
            "pre-deploy-local": "",
            "post-deploy":
                "pnpm install --frozen-lockfile && pnpm build && pm2 reload ecosystem.config.js --env production",
            "pre-setup": "",
        },
    },
};
