# CivicPoll Deployment Setup

## Overview

This document outlines the complete deployment infrastructure for CivicPoll, tailored to its specific needs as a full-stack civic engagement platform.

## What's Been Created

### 1. CI/CD Configuration
- **`.gitlab-ci.yml`**: Complete pipeline with test, build, and multi-environment deployment stages
- **`cicd_vars.sh`**: Environment variable validation for all CivicPoll-specific requirements

### 2. Ansible Deployment Infrastructure
- **`ansible/playbook.yaml`**: Main deployment orchestration
- **`ansible/auth-secret.yaml`**: Automatic secret generation for Strapi and authentication
- **`ansible/nodejs.yaml`**: Node.js 20 setup with pnpm and PM2
- **`ansible/postgresql.yaml`**: PostgreSQL with PostGIS for geolocation features
- **`ansible/port_check.yaml`**: Port conflict resolution for multi-service architecture
- **`ansible/nginx_config.yaml`**: Nginx reverse proxy configuration
- **`ansible/ssl_setup.yaml`**: Automatic SSL certificate management

### 3. Environment Templates
- **`ansible/backend.env.j2`**: Strapi backend environment configuration
- **`ansible/frontend.env.j2`**: Next.js frontend environment configuration
- **`ansible/nginx.conf.j2`**: Nginx configuration with security headers and rate limiting

### 4. Enhanced PM2 Configuration
- **`ecosystem.config.js`**: Updated for CivicPoll's dual-service architecture

## Key Features

### Architecture-Specific
- ✅ **Monorepo support**: Handles both frontend and backend builds
- ✅ **Multi-service deployment**: Separate PM2 processes for frontend and backend
- ✅ **Environment-specific ports**: Development (3010/1347), Staging (3020/1357), Production (3000/1337)
- ✅ **PostgreSQL + PostGIS**: Full geolocation database support

### Security & Compliance
- ✅ **GDPR compliance**: Headers and configuration for data protection
- ✅ **Rate limiting**: API and frontend protection
- ✅ **SSL automation**: Let's Encrypt integration
- ✅ **Security headers**: CSP, HSTS, XSS protection
- ✅ **Secure secrets**: Automatic generation and management

### CivicPoll-Specific
- ✅ **Strapi configuration**: All required secrets and database setup
- ✅ **GDPR plugin support**: Environment variables for compliance features
- ✅ **Audit logging**: Built-in security monitoring
- ✅ **SendGrid integration**: Optional email service configuration
- ✅ **Geolocation features**: PostGIS database extension

### DevOps Excellence
- ✅ **Multi-environment**: Development, staging, and production
- ✅ **Health monitoring**: Automatic service health checks
- ✅ **Log management**: Centralized logging for both services
- ✅ **Graceful deployments**: Zero-downtime updates
- ✅ **Rollback capability**: PM2 process management

## Environment Structure

```
Development:
├── Frontend: civicpoll-dev.fr.smatflow.xyz:3010
├── Backend:  civicpoll-dev.fr.smatflow.xyz:1347
└── Auto-deploy: On every main branch push

Staging:
├── Frontend: civicpoll-staging.fr.smatflow.xyz:3020
├── Backend:  civicpoll-staging.fr.smatflow.xyz:1357
└── Manual deploy: From GitLab UI

Production:
├── Frontend: https://civicpoll.fr.smatflow.xyz
├── Backend:  https://civicpoll.fr.smatflow.xyz/api
├── SSL: Automatic Let's Encrypt
└── Manual deploy: From GitLab UI
```

## Next Steps

1. **Set up GitLab CI Variables**: Configure all required environment variables in GitLab
2. **Prepare target servers**: Ensure deployment servers are accessible
3. **Database setup**: Configure PostgreSQL with PostGIS
4. **Domain configuration**: Point domains to deployment servers
5. **Test deployment**: Start with development environment

## Benefits Over Standard Setup

This deployment setup is specifically tailored for CivicPoll's requirements:

- **Civic compliance**: Built-in GDPR and audit logging support
- **Geolocation ready**: PostGIS database for mapping features
- **French regulations**: Security headers and data protection
- **Scalable architecture**: Supports growth from local to national scale
- **Professional monitoring**: Enterprise-level logging and health checks

The infrastructure supports CivicPoll's mission of providing secure, compliant, and scalable civic engagement tools for French democracy.
